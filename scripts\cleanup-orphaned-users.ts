#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupOrphanedUsers() {
  try {
    console.log('Checking for orphaned users...');
    
    // Find users with no accounts
    const orphanedUsers = await prisma.user.findMany({
      where: {
        accounts: {
          none: {}
        }
      },
      include: {
        accounts: true,
        sessions: true
      }
    });

    console.log(`Found ${orphanedUsers.length} orphaned users`);

    for (const user of orphanedUsers) {
      console.log(`Deleting orphaned user: ${user.email} (ID: ${user.id})`);
      
      // Delete associated sessions first
      await prisma.session.deleteMany({
        where: { userId: user.id }
      });
      
      // Delete the user
      await prisma.user.delete({
        where: { id: user.id }
      });
      
      console.log(`✓ Deleted user: ${user.email}`);
    }

    console.log('Cleanup completed successfully');
  } catch (error) {
    console.error('Cleanup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupOrphanedUsers();
