import { NextResponse } from 'next/server';
import { cleanupExpiredSessions, getSessionStats, cleanupAllDatabaseSessions } from '@/app/lib/session-cleanup';

export async function GET() {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Session cleanup API not available in production' },
      { status: 403 }
    );
  }

  try {
    const stats = await getSessionStats();

    return NextResponse.json({
      message: 'Session statistics retrieved',
      stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Session stats error:', error);
    return NextResponse.json(
      { error: 'Failed to get session statistics' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Session cleanup API not available in production' },
      { status: 403 }
    );
  }

  try {
    const body = await request.json().catch(() => ({}));
    const { action } = body;

    switch (action) {
      case 'cleanup-expired':
        const cleanupResult = await cleanupExpiredSessions();
        return NextResponse.json({
          message: 'Expired sessions cleanup completed',
          result: cleanupResult,
          timestamp: new Date().toISOString(),
        });

      case 'cleanup-all-database':
        const deletedCount = await cleanupAllDatabaseSessions();
        return NextResponse.json({
          message: 'All database sessions cleared',
          deletedCount,
          timestamp: new Date().toISOString(),
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use "cleanup-expired" or "cleanup-all-database"' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Session cleanup error:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup sessions' },
      { status: 500 }
    );
  }
}
