'use client';

import Link from 'next/link';
import { useAuthState } from '@/app/contexts/AuthStateContext';

export default function EmailButton() {
  const { isAnyAuthLoading, isAuthCompleted } = useAuthState();
  const isDisabled = isAnyAuthLoading || isAuthCompleted;

  return (
    <Link
      href="/passwordless"
      className={`flex items-center justify-center gap-3 rounded-lg bg-white px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium text-gray-700 shadow-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full transition-colors cursor-pointer ${
        isDisabled ? 'opacity-50 cursor-not-allowed hover:bg-white pointer-events-none' : ''
      }`}
      aria-disabled={isDisabled}
    >
      <svg
        className="w-5 h-5 text-gray-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
        />
      </svg>
      Email
    </Link>
  );
}
