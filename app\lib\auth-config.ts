import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import EmailProvider from 'next-auth/providers/email';
import { CustomPrismaAdapter } from './prisma-adapter';
import { sendVerificationRequest } from './email';



const googleClientId = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';
const githubClientId = process.env.GITHUB_ID || '';
const githubClientSecret = process.env.GITHUB_SECRET || '';
const amazonClientId = process.env.AMAZON_CLIENT_ID || '';
const amazonClientSecret = process.env.AMAZON_CLIENT_SECRET || '';
const resendApiKey = process.env.RESEND_API_KEY || '';
const resendSender = process.env.RESEND_SENDER || '';

// Custom Amazon OAuth Provider
const AmazonProvider = {
  id: 'amazon',
  name: 'Amazon',
  type: 'oauth' as const,
  authorization: {
    url: 'https://www.amazon.com/ap/oa',
    params: {
      scope: 'profile',
      response_type: 'code',
    },
  },
  token: 'https://api.amazon.com/auth/o2/token',
  userinfo: 'https://api.amazon.com/user/profile',
  clientId: amazonClientId,
  clientSecret: amazonClientSecret,
  profile(profile: { user_id: string; name: string; email: string }) {
    return {
      id: profile.user_id,
      name: profile.name,
      email: profile.email,
      image: null, // Amazon doesn't provide profile images via this API
    };
  },
};

if (!googleClientId || !googleClientSecret) {
  throw new Error('Missing Google OAuth credentials');
}

if (!githubClientId || !githubClientSecret) {
  throw new Error('Missing GitHub OAuth credentials');
}

if (!amazonClientId || !amazonClientSecret) {
  throw new Error('Missing Amazon OAuth credentials');
}

if (!resendApiKey || !resendSender) {
  throw new Error('Missing Resend email credentials');
}

export const authOptions: NextAuthOptions = {
  adapter: CustomPrismaAdapter(),
  // Enable debug mode to see more detailed logs
  debug: process.env.NODE_ENV === 'development',
  providers: [
    GoogleProvider({
      clientId: googleClientId,
      clientSecret: googleClientSecret,
    }),
    GitHubProvider({
      clientId: githubClientId,
      clientSecret: githubClientSecret,
    }),
    AmazonProvider,
    EmailProvider({
      server: {
        host: 'smtp.resend.com',
        port: 587,
        auth: {
          user: 'resend',
          pass: resendApiKey,
        },
      },
      from: resendSender,
      sendVerificationRequest,
      // Extend magic link expiration to 24 hours
      maxAge: 24 * 60 * 60, // 24 hours in seconds
      // Ensure proper URL generation for development
      generateVerificationToken: undefined, // Use default
    }),
  ],
  session: {
    // Using JWT strategy: sessions stored in tokens, not database
    // This means the Session table in Prisma schema is unused (kept for future flexibility)
    // To switch to database sessions, change to: strategy: 'database'
    strategy: 'jwt',
    // Extend session duration to 30 days
    maxAge: 30 * 24 * 60 * 60, // 30 days in seconds
    // Update session every 24 hours
    updateAge: 24 * 60 * 60, // 24 hours in seconds
  },
  pages: {
    signIn: '/signin',
    error: '/signin',
    verifyRequest: '/passwordless', // Redirect here after email is sent
  },
  // Ensure proper URL handling
  useSecureCookies: process.env.NODE_ENV === 'production',
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production' ? '__Secure-next-auth.session-token' : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    }
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      // Debug logging for OAuth sign-in attempts
      console.log('SignIn callback:', {
        user: user?.email,
        provider: account?.provider,
        accountId: account?.providerAccountId,
        userExists: !!user?.id,
      });

      // Log profile information for debugging
      if (profile) {
        console.log('Profile data:', {
          email: profile.email,
          name: profile.name,
          provider: account?.provider
        });
      }

      // Manual account linking for OAuth providers
      if (account?.type === 'oauth' && profile?.email) {
        try {
          // Import prisma here to avoid circular dependencies
          const { prisma } = await import('./prisma');

          // Check if user exists with this email
          const existingUser = await prisma.user.findUnique({
            where: { email: profile.email },
            include: { accounts: true }
          });

          if (existingUser) {
            // Check if this provider account already exists
            const existingAccount = existingUser.accounts.find(
              acc => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId
            );

            if (!existingAccount) {
              // Link the new provider account to the existing user
              console.log(`Manually linking ${account.provider} account to existing user`);
              await prisma.account.create({
                data: {
                  userId: existingUser.id,
                  type: account.type,
                  provider: account.provider,
                  providerAccountId: account.providerAccountId,
                  access_token: account.access_token,
                  token_type: account.token_type,
                  scope: account.scope,
                  refresh_token: account.refresh_token,
                  expires_at: account.expires_at,
                  id_token: account.id_token,
                  session_state: account.session_state,
                }
              });
              console.log(`Successfully linked ${account.provider} account`);
            }
          }
        } catch (error) {
          console.error('Manual account linking failed:', error);
          return false;
        }
      }

      console.log('OAuth sign-in approved for provider:', account?.provider);
      return true;
    },
    async jwt({ token, user, account }) {
      // Include user info in JWT token on sign in
      if (user) {
        token.id = user.id;
        token.provider = account?.provider;
      }
      return token;
    },
    async session({ session, token }) {
      // Include user info from JWT token in session
      if (session.user && token) {
        session.user.id = token.id as string;
        session.user.provider = token.provider as string;
      }
      return session;
    },
    async redirect({ baseUrl }) {
      // Always redirect to dashboard after successful authentication
      return `${baseUrl}/dashboard`;
    },
  },
};
