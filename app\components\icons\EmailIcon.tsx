interface EmailIconProps {
  size?: number;
  className?: string;
}

export default function EmailIcon({ size = 20, className = "" }: EmailIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      aria-label="Email"
    >
      <path
        d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
      />
      <polyline
        points="22,6 12,13 2,6"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
      />
    </svg>
  );
}
