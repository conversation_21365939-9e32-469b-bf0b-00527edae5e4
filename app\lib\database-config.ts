/**
 * Database configuration utility
 * Provides environment-specific database URLs and settings
 */

export function getDatabaseUrl(): string {
  const baseUrl = process.env.DATABASE_URL;
  
  if (!baseUrl) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  // For SQLite, use different files for different environments
  if (baseUrl.startsWith('file:')) {
    const environment = process.env.NODE_ENV || 'development';
    
    switch (environment) {
      case 'production':
        return 'file:./prod.db';
      case 'test':
        return 'file:./test.db';
      case 'development':
      default:
        return 'file:./dev.db';
    }
  }

  // For other databases (PostgreSQL, MySQL, etc.), return as-is
  return baseUrl;
}

export function getEnvironmentInfo() {
  return {
    environment: process.env.NODE_ENV || 'development',
    databaseUrl: getDatabaseUrl(),
    isProduction: process.env.NODE_ENV === 'production',
    isDevelopment: process.env.NODE_ENV === 'development',
  };
}
