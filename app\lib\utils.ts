/**
 * Utility functions for the application
 */

/**
 * Formats provider names to proper case
 * @param provider - The provider name (e.g., "google", "github", "email")
 * @returns The formatted provider name (e.g., "Google", "GitHub", "Email")
 */
export function formatProviderName(provider: string): string {
  if (!provider) {
    return '';
  }

  const lowercaseProvider = provider.toLowerCase();
  
  // Handle special cases
  switch (lowercaseProvider) {
    case 'github':
      return 'GitHub';
    case 'google':
      return 'Google';
    case 'email':
      return 'Email';
    default:
      // For any other providers, capitalize first letter
      return provider.charAt(0).toUpperCase() + provider.slice(1).toLowerCase();
  }
}
