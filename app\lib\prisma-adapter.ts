import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma, ensureDatabaseReady } from './prisma';
import type { Adapter, AdapterUser, AdapterAccount, AdapterSession, VerificationToken } from 'next-auth/adapters';

/**
 * Custom Prisma adapter that ensures database initialization
 * before any database operations in serverless environments
 */
export function CustomPrismaAdapter(): Adapter {
  const baseAdapter = PrismaAdapter(prisma);

  // Wrap all adapter methods to ensure database is ready
  return {
    ...baseAdapter,
    
    async createUser(user: Omit<AdapterUser, "id">) {
      await ensureDatabaseReady();
      console.log('CreateUser called:', user.email);
      try {
        const result = await baseAdapter.createUser!(user);
        console.log('CreateUser successful:', result.id);
        return result;
      } catch (error) {
        console.error('CreateUser failed:', error);
        throw error;
      }
    },

    async getUser(id: string) {
      await ensureDatabaseReady();
      return baseAdapter.getUser!(id);
    },

    async getUserByEmail(email: string) {
      await ensureDatabaseReady();
      console.log('GetUserByEmail called:', email);
      const result = await baseAdapter.getUserByEmail!(email);
      console.log('GetUserByEmail result:', result ? 'User found' : 'No user found');

      // If user found, check what accounts are linked
      if (result) {
        try {
          const accounts = await prisma.account.findMany({
            where: { userId: result.id },
            select: { provider: true, type: true }
          });
          console.log('Existing accounts for user:', accounts);
        } catch (error) {
          console.error('Error checking accounts:', error);
        }
      }

      return result;
    },

    async getUserByAccount({ providerAccountId, provider }: { providerAccountId: string; provider: string }) {
      await ensureDatabaseReady();
      console.log('GetUserByAccount called:', { provider, providerAccountId });
      const result = await baseAdapter.getUserByAccount!({ providerAccountId, provider });
      console.log('GetUserByAccount result:', result ? 'User found' : 'No user found');
      return result;
    },

    async updateUser(user: Partial<AdapterUser> & Pick<AdapterUser, "id">) {
      await ensureDatabaseReady();
      return baseAdapter.updateUser!(user);
    },

    async deleteUser(userId: string): Promise<void> {
      await ensureDatabaseReady();
      await baseAdapter.deleteUser!(userId);
    },

    async linkAccount(account: AdapterAccount) {
      await ensureDatabaseReady();
      console.log('LinkAccount called:', {
        provider: account.provider,
        providerAccountId: account.providerAccountId,
        userId: account.userId,
        type: account.type
      });
      try {
        const result = await baseAdapter.linkAccount!(account);
        console.log('LinkAccount successful');
        return result;
      } catch (error) {
        console.error('LinkAccount failed:', error);
        throw error;
      }
    },

    async unlinkAccount({ providerAccountId, provider }: { providerAccountId: string; provider: string }) {
      await ensureDatabaseReady();
      return baseAdapter.unlinkAccount!({ providerAccountId, provider });
    },

    async createSession({ sessionToken, userId, expires }: { sessionToken: string; userId: string; expires: Date }) {
      await ensureDatabaseReady();
      return baseAdapter.createSession!({ sessionToken, userId, expires });
    },

    async getSessionAndUser(sessionToken: string) {
      await ensureDatabaseReady();
      return baseAdapter.getSessionAndUser!(sessionToken);
    },

    async updateSession({ sessionToken, ...session }: Partial<AdapterSession> & Pick<AdapterSession, "sessionToken">) {
      await ensureDatabaseReady();
      return baseAdapter.updateSession!({ sessionToken, ...session });
    },

    async deleteSession(sessionToken: string): Promise<void> {
      await ensureDatabaseReady();
      await baseAdapter.deleteSession!(sessionToken);
    },

    async createVerificationToken({ identifier, expires, token }: VerificationToken) {
      await ensureDatabaseReady();
      return baseAdapter.createVerificationToken!({ identifier, expires, token });
    },

    async useVerificationToken({ identifier, token }: { identifier: string; token: string }) {
      await ensureDatabaseReady();
      return baseAdapter.useVerificationToken!({ identifier, token });
    },
  };
}
