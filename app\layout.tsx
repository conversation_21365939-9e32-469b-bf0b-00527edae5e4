import type { Metada<PERSON> } from "next";
import { Geist, <PERSON>eist_Mono } from "next/font/google";
import { NextAuthProvider } from "./providers";
import ErrorBoundary from "./components/ErrorBoundary";
import Footer from "./components/Footer";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "React Next.js Learn Auth",
  description: "A comprehensive authentication learning project built with Next.js 15, NextAuth.js, and Prisma. Demonstrates OAuth providers, passwordless email authentication, and secure session management.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <ErrorBoundary>
          <NextAuthProvider>
            <div className="flex flex-col min-h-screen">
              <main className="flex-1">
                {children}
              </main>
              <Footer />
            </div>
          </NextAuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}

