#!/bin/bash
set -e

# NextAuth.js Learning Project Environment Setup Script (Template)
# This script configures the development environment for remote agents
# with all necessary dependencies, environment variables, and Git configuration
#
# SECURITY NOTE: This template uses environment variable references.
# Actual values should be provided via secure environment variable injection
# or local .env files. Remote agents may have limited functionality without
# proper secret management.

echo "Setting up Next.js Auth environment for remote agent..."

# Update package lists
sudo apt-get update

# Install Node.js and pnpm (if not already available in base environment)
echo "Ensuring Node.js and pnpm are available..."
npm install -g pnpm

# Configure Git with agent credentials
echo "Configuring Git credentials for agent..."
git config --global user.name "${AI_AGENT_NAME}"
git config --global user.email "${AI_AGENT_EMAIL}"

# Set up environment variables for the session
echo "Setting up environment variables..."
echo "WARNING: Using template values - some functionality may be limited without proper secrets"

# Resend configuration for email functionality
export RESEND_API_KEY="${RESEND_API_KEY:-your-resend-api-key}"
export RESEND_DOMAIN="${RESEND_DOMAIN:-your-domain.com}"
export RESEND_SENDER="${RESEND_SENDER:-<EMAIL>}"
export RESEND_RECIPIENT="${RESEND_RECIPIENT:-<EMAIL>}"

# NextAuth configuration
export NEXTAUTH_SECRET="${NEXTAUTH_SECRET:-your-nextauth-secret-32-chars-min}"
export NEXTAUTH_URL="${NEXTAUTH_URL:-http://localhost:3000}"

# OAuth provider credentials
export GOOGLE_CLIENT_ID="${GOOGLE_CLIENT_ID:-your-google-client-id}"
export GOOGLE_CLIENT_SECRET="${GOOGLE_CLIENT_SECRET:-your-google-client-secret}"
export GITHUB_ID="${GITHUB_ID:-your-github-client-id}"
export GITHUB_SECRET="${GITHUB_SECRET:-your-github-client-secret}"
export AMAZON_CLIENT_ID="${AMAZON_CLIENT_ID:-your-amazon-client-id}"
export AMAZON_CLIENT_SECRET="${AMAZON_CLIENT_SECRET:-your-amazon-client-secret}"

# Database configuration
export DATABASE_URL="${DATABASE_URL:-file:./dev.db}"

# Cron job security
export CRON_SECRET="${CRON_SECRET:-your-cron-secret-key}"

# Add environment variables to the shell profile for persistence
echo "Making environment variables persistent..."
cat >> ~/.bashrc << 'EOF'

# Next.js Auth Project Environment Variables (Template)
export RESEND_API_KEY="${RESEND_API_KEY:-your-resend-api-key}"
export RESEND_DOMAIN="${RESEND_DOMAIN:-your-domain.com}"
export RESEND_SENDER="${RESEND_SENDER:-<EMAIL>}"
export RESEND_RECIPIENT="${RESEND_RECIPIENT:-<EMAIL>}"
export NEXTAUTH_SECRET="${NEXTAUTH_SECRET:-your-nextauth-secret-32-chars-min}"
export NEXTAUTH_URL="${NEXTAUTH_URL:-http://localhost:3000}"
export GOOGLE_CLIENT_ID="${GOOGLE_CLIENT_ID:-your-google-client-id}"
export GOOGLE_CLIENT_SECRET="${GOOGLE_CLIENT_SECRET:-your-google-client-secret}"
export GITHUB_ID="${GITHUB_ID:-your-github-client-id}"
export GITHUB_SECRET="${GITHUB_SECRET:-your-github-client-secret}"
export AMAZON_CLIENT_ID="${AMAZON_CLIENT_ID:-your-amazon-client-id}"
export AMAZON_CLIENT_SECRET="${AMAZON_CLIENT_SECRET:-your-amazon-client-secret}"
export DATABASE_URL="${DATABASE_URL:-file:./dev.db}"
export CRON_SECRET="${CRON_SECRET:-your-cron-secret-key}"
EOF

# Navigate to workspace and install dependencies
echo "Installing project dependencies..."
cd /mnt/persist/workspace

# Install dependencies using pnpm
pnpm install

# Create .env.local file in the workspace (Template)
echo "Creating .env.local template file..."
cat > .env.local << 'EOF'
# NextAuth.js Environment Variables Template
# Replace placeholder values with actual credentials for local development

GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID:-your-google-client-id}
GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET:-your-google-client-secret}
GITHUB_ID=${GITHUB_ID:-your-github-client-id}
GITHUB_SECRET=${GITHUB_SECRET:-your-github-client-secret}
NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-your-nextauth-secret-32-chars-min}
NEXTAUTH_URL=${NEXTAUTH_URL:-http://localhost:3000}
RESEND_API_KEY=${RESEND_API_KEY:-your-resend-api-key}
RESEND_DOMAIN=${RESEND_DOMAIN:-your-domain.com}
RESEND_SENDER=${RESEND_SENDER:-<EMAIL>}
RESEND_RECIPIENT=${RESEND_RECIPIENT:-<EMAIL>}
DATABASE_URL=${DATABASE_URL:-"file:./dev.db"}
CRON_SECRET=${CRON_SECRET:-your-cron-secret-key}
AI_AGENT_EMAIL=${AI_AGENT_EMAIL:-<EMAIL>}
AI_AGENT_GITHUB_TOKEN=${AI_AGENT_GITHUB_TOKEN:-your-github-token}
AMAZON_CLIENT_ID=${AMAZON_CLIENT_ID:-your-amazon-client-id}
AMAZON_CLIENT_SECRET=${AMAZON_CLIENT_SECRET:-your-amazon-client-secret}
EOF

echo "Environment setup complete!"
echo "Git user: $(git config --global user.name)"
echo "Git email: $(git config --global user.email)"
echo "RESEND_API_KEY: ${RESEND_API_KEY:0:10}..."
echo "RESEND_DOMAIN: $RESEND_DOMAIN"
echo "RESEND_SENDER: $RESEND_SENDER"
