'use client';

import { createContext, useContext, useState, useCallback, useEffect, useRef, ReactNode } from 'react';

interface AuthStateContextType {
  isAnyAuthLoading: boolean;
  showLoading: boolean;
  activeProvider: string | null;
  isAuthCompleted: boolean;
  setAuthLoading: (provider: string) => void;
  setAuthCompleted: () => void;
  resetAuthState: () => void;
}

const AuthStateContext = createContext<AuthStateContextType | undefined>(undefined);

interface AuthStateProviderProps {
  children: ReactNode;
}

export function AuthStateProvider({ children }: AuthStateProviderProps) {
  const [isAnyAuthLoading, setIsAnyAuthLoading] = useState(false);
  const [showLoading, setShowLoading] = useState(false);
  const [activeProvider, setActiveProvider] = useState<string | null>(null);
  const [isAuthCompleted, setIsAuthCompleted] = useState(false);
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const loadingStartTimeRef = useRef<number | null>(null);

  // Set authentication as completed (keeps buttons disabled)
  const setAuthCompleted = useCallback(() => {
    setIsAuthCompleted(true);
    setShowLoading(false);
    loadingStartTimeRef.current = null;
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
      loadingTimerRef.current = null;
    }
  }, []);

  // Reset function to clear all states and timers (only for errors)
  const resetAuthState = useCallback(() => {
    setIsAnyAuthLoading(false);
    setShowLoading(false);
    setActiveProvider(null);
    setIsAuthCompleted(false);
    loadingStartTimeRef.current = null;
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
      loadingTimerRef.current = null;
    }
  }, []);

  // Set loading state for a specific provider
  const setAuthLoading = useCallback((provider: string) => {
    setIsAnyAuthLoading(true);
    setActiveProvider(provider);
    loadingStartTimeRef.current = Date.now();

    // Set a 500ms delay before showing loading spinner
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
    }

    loadingTimerRef.current = setTimeout(() => {
      setShowLoading(true);
    }, 500);
  }, []);

  // Set up event listeners for page visibility and focus changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Only reset if page becomes visible and loading has been active for >2 seconds
      // This prevents false positives from quick tab switches
      if (document.visibilityState === 'visible' && loadingStartTimeRef.current) {
        const loadingDuration = Date.now() - loadingStartTimeRef.current;
        if (loadingDuration > 2000) {
          resetAuthState();
        }
      }
    };

    const handleFocus = () => {
      // Reset on window focus if loading has been active for >2 seconds
      // This handles cases where user navigates back via browser controls
      if (loadingStartTimeRef.current) {
        const loadingDuration = Date.now() - loadingStartTimeRef.current;
        if (loadingDuration > 2000) {
          resetAuthState();
        }
      }
    };

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    // Cleanup function
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
      }
    };
  }, [resetAuthState]);

  const value: AuthStateContextType = {
    isAnyAuthLoading,
    showLoading,
    activeProvider,
    isAuthCompleted,
    setAuthLoading,
    setAuthCompleted,
    resetAuthState,
  };

  return (
    <AuthStateContext.Provider value={value}>
      {children}
    </AuthStateContext.Provider>
  );
}

export function useAuthState(): AuthStateContextType {
  const context = useContext(AuthStateContext);
  if (context === undefined) {
    throw new Error('useAuthState must be used within an AuthStateProvider');
  }
  return context;
}
